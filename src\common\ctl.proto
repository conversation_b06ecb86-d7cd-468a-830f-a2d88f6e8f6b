syntax = "proto2";
package ctl;

message CtlReq {
  enum Node {
    NODE_ALL     = 0;
    NODE_SERVER  = 1;
    NODE_AGENT   = 2;
    NODE_PROBE   = 3;
  };
  required Node node      = 1;
  enum Srvice {
    SRV_ALL    = 0;
    SRV_SSH    = 1;
    SRV_HTTP   = 2;
    SRV_PROBE  = 3;
    SRV_CAP    = 4;
    SRV_FSD    = 5;
    SRV_DISK   = 6;
    SRV_BASIC  = 7;
  };
  optional Srvice srv     = 2;
  enum Operate {
    STATUS  = 0;
    START   = 1;
    STOP    = 2;
    RESTART = 3;
  };
  required Operate op     = 3;
  optional string id      = 4 [default = "0"];
}

message CtlRecord {
  optional string node    = 1;
  optional string srv     = 2;
  optional string op      = 3;
  optional uint32 agentid = 4;
  optional uint32 devid   = 5;
  optional string status  = 6;
  optional string desc    = 7;
  optional string result  = 8;
  optional uint32 id      = 9;
  optional string name    = 10;
  optional string ip      = 11;
  optional uint32 relate_server = 12;
  optional uint32 relate_agent  = 13;
}

message CtlResponse {
  repeated CtlRecord records = 1; 
}
